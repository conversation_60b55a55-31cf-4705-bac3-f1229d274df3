"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { profileSchema, ProfileFormValues } from '../schemas';
import { Copy, RefreshCw, Loader2, Eye, AlertTriangle } from 'lucide-react';

interface ProfileFormProps {
  initialData: {
    accessKey: string;
    name?: string;
  };
  onProfileUpdate: (forceRefresh?: boolean) => Promise<void> | void;
}

export function ProfileForm({ initialData, onProfileUpdate }: ProfileFormProps) {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [previewKey, setPreviewKey] = useState<string>('');
  const [showRegenerateDialog, setShowRegenerateDialog] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [isSavingNewKey, setIsSavingNewKey] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: initialData.name || '',
    },
  });

  // Update form when initialData changes
  useEffect(() => {
    console.log('ProfileForm: initialData changed:', initialData);
    form.reset({
      name: initialData.name || '',
    });
  }, [initialData.name, form]);

  const copyToClipboard = async (key?: string) => {
    try {
      const keyToCopy = key || initialData.accessKey;
      await navigator.clipboard.writeText(keyToCopy);
      toast.success('Access key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const generateKeyPreview = async () => {
    setIsGeneratingPreview(true);

    try {
      console.log('Generating key preview...');
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'generate'
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Key preview response:', data);

      if (response.ok) {
        // Handle both possible response structures
        const newKey = data.data?.newAccessKey || data.newAccessKey || data.data?.accessKey || data.accessKey;
        if (newKey) {
          setPreviewKey(newKey);
          setShowRegenerateDialog(true);
          toast.success('New key preview generated!');
        } else {
          console.error('No access key in response:', data);
          toast.error('Failed to generate key preview - no key received');
        }
      } else {
        const errorMessage = data.error?.message || data.message || 'Failed to generate key preview';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Key preview generation error:', error);
      toast.error('Failed to generate key preview');
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const saveProfile = async (values: ProfileFormValues) => {
    setIsSaving(true);

    try {
      console.log('Saving profile with values:', values);
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Profile save response:', data);

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to update profile';
        throw new Error(errorMessage);
      }

      // Refresh profile data and wait for it to complete
      await onProfileUpdate(true);

      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const savePreviewKey = async () => {
    if (!previewKey) {
      toast.error('No preview key to save');
      return;
    }

    setIsSavingNewKey(true);
    setShowRegenerateDialog(false);

    try {
      console.log('Saving preview key as new access key:', previewKey);

      // Use the regenerate API but with the preview key
      const response = await fetch('/api/auth/regenerate-key', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey,
          newAccessKey: previewKey // Pass the preview key to save
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Save preview key response:', data);

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to save new access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      await onProfileUpdate(true);

      toast.success('New access key saved successfully!');
    } catch (error: any) {
      console.error('Save preview key error:', error);
      toast.error(error.message || 'Failed to save new access key');
    } finally {
      setIsSavingNewKey(false);
      setPreviewKey('');
    }
  };

  const confirmRegenerateKey = async () => {
    setIsRegenerating(true);
    setShowRegenerateDialog(false);

    try {
      console.log('Regenerating access key (random generation)...');
      const response = await fetch('/api/auth/regenerate-key', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey
          // No newAccessKey provided - will generate random
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Regenerate key response:', data);

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to regenerate access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      await onProfileUpdate(true);

      toast.success('Access key regenerated successfully! Please save your new key.');
    } catch (error: any) {
      console.error('Key regeneration error:', error);
      toast.error(error.message || 'Failed to regenerate access key');
    } finally {
      setIsRegenerating(false);
      setPreviewKey('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Information Form */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-green-800 dark:text-green-200">
            Profile Information
          </CardTitle>
          <CardDescription className="text-green-600 dark:text-green-400">
            Manage your profile details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(saveProfile)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Display Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your name (optional)"
                        {...field}
                        maxLength={50}
                        className="h-10"
                      />
                    </FormControl>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground">
                      This name will be displayed in your navbar and profile
                    </p>
                  </FormItem>
                )}
              />

              <div className="flex justify-end pt-2">
                <Button
                  type="submit"
                  disabled={isSaving}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Profile'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Access Key Management */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-blue-800 dark:text-blue-200">
            Access Key
          </CardTitle>
          <CardDescription className="text-blue-600 dark:text-blue-400">
            Your unique access key for authentication
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg border">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
                <label className="text-sm font-medium">Your Access Key</label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard()}
                    className="h-8 px-3"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                  <Dialog open={showRegenerateDialog} onOpenChange={setShowRegenerateDialog}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={generateKeyPreview}
                        disabled={isRegenerating || isGeneratingPreview}
                        className="h-8 px-3 border-orange-300 hover:bg-orange-100 dark:border-orange-700 dark:hover:bg-orange-900"
                      >
                        {isGeneratingPreview ? (
                          <>
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Regenerate
                          </>
                        )}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <AlertTriangle className="h-5 w-5 text-orange-500" />
                          Regenerate Access Key
                        </DialogTitle>
                        <DialogDescription>
                          This will replace your current access key. Make sure to save the new key safely.
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Current Key:</label>
                          <div className="font-mono text-sm p-2 bg-muted rounded border mt-1">
                            {initialData.accessKey}
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-green-600 dark:text-green-400">New Key Preview:</label>
                          <div className="font-mono text-sm p-2 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800 mt-1 font-semibold">
                            {previewKey || 'Generating...'}
                          </div>
                          <div className="flex gap-2 mt-2">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(previewKey)}
                              disabled={!previewKey}
                              className="h-7 px-2 text-xs"
                            >
                              <Copy className="h-3 w-3 mr-1" />
                              Copy New Key
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={generateKeyPreview}
                              disabled={isGeneratingPreview}
                              className="h-7 px-2 text-xs text-blue-600 hover:text-blue-700"
                            >
                              {isGeneratingPreview ? (
                                <>
                                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                  Generating...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="h-3 w-3 mr-1" />
                                  Generate Different Key
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>

                      <DialogFooter className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setShowRegenerateDialog(false);
                            setPreviewKey('');
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={savePreviewKey}
                          disabled={isSavingNewKey || !previewKey}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {isSavingNewKey ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            'Save This Key'
                          )}
                        </Button>
                        <Button
                          type="button"
                          onClick={confirmRegenerateKey}
                          disabled={isRegenerating || !previewKey}
                          className="bg-orange-600 hover:bg-orange-700"
                        >
                          {isRegenerating ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Regenerating...
                            </>
                          ) : (
                            'Generate & Save Random Key'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <div className="font-mono text-lg font-bold p-4 bg-white dark:bg-gray-900 rounded-lg border-2 border-blue-300 dark:border-blue-700 text-center shadow-sm">
                {initialData.accessKey}
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300 mt-3 flex items-start gap-2">
                <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>This is your unique access key. Keep it safe and don't share it with anyone.</span>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground space-y-3">
                <div className="flex items-start gap-2">
                  <div className="text-lg">🔐</div>
                  <div>
                    <p className="font-semibold text-foreground mb-2">Privacy-focused account</p>
                    <ul className="space-y-1.5 text-xs">
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-0.5">•</span>
                        <span>No personal information stored beyond what you choose to add</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-blue-500 mt-0.5">•</span>
                        <span>Access key is your primary identifier</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-orange-500 mt-0.5">•</span>
                        <span>Preview new keys before regenerating for security</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-purple-500 mt-0.5">•</span>
                        <span>New keys are immediately saved to your account</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
